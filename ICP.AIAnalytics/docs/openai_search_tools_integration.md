# OpenAI Search Tools Integration with LangChain

## Overview

This document describes the integration of OpenAI's function calling capabilities with LangChain to provide real-time web search functionality for exchange rates and currency conversion in the XML report validation system.

## Architecture

### Components

1. **WebSearchService** (`app/services/web_search_service.py`)
   - Core service for fetching exchange rates from multiple APIs
   - Implements caching with 1-hour TTL
   - Provides fallback mechanisms for API failures
   - Supports major currencies including XPF, USD, EUR, GBP, JPY, etc.

2. **OpenAI Search Tools** (`app/services/openai_search_tools.py`)
   - LangChain tools decorated with `@tool` for OpenAI function calling
   - Provides structured interfaces for currency operations
   - Returns JSON responses for easy parsing

3. **ValidationService Integration** (`app/services/validation_service.py`)
   - Enhanced to detect currency-related questions
   - Uses LLM with function calling when appropriate
   - Maintains backward compatibility with existing validation logic

## Key Features

### Real-Time Exchange Rates
- Fetches current exchange rates from multiple APIs
- Primary: exchangerate-api.com
- Secondary: fixer.io
- Fallback: hardcoded rates for common currencies

### Currency Conversion
- Converts amounts between any supported currencies
- Provides detailed calculation information
- Handles edge cases and error scenarios

### Credit Classification
- Automatically converts credit amounts to GBP
- Classifies as Small (< £50,000), Medium (£50,000-£250,000), or Large (> £250,000)
- Provides reasoning for classification decisions

### Intelligent Question Detection
- Automatically detects currency-related validation questions
- Uses appropriate LLM (with or without tools) based on question type
- Optimizes performance by only using tools when needed

## Available Tools

### 1. search_exchange_rate
```python
await search_exchange_rate("XPF", "GBP")
```
Returns current exchange rate between two currencies.

### 2. convert_currency_amount
```python
await convert_currency_amount(1000000, "XPF", "GBP")
```
Converts a specific amount using current exchange rates.

### 3. get_currency_info
```python
await get_currency_info("XPF")
```
Retrieves detailed information about a currency.

### 4. classify_credit_amount_with_conversion
```python
await classify_credit_amount_with_conversion(1000000, "XPF", "GBP")
```
Converts amount to GBP and classifies credit size.

### 5. search_web_for_exchange_rates
```python
await search_web_for_exchange_rates("XPF to GBP exchange rate")
```
General web search for exchange rate information.

## Configuration

### Environment Variables
```bash
# Required for OpenAI function calling
OPENAI_API_KEY=your_openai_api_key

# Optional: Exchange rate API keys
EXCHANGERATE_API_KEY=your_exchangerate_api_key
FIXER_API_KEY=your_fixer_api_key
```

### Settings (`app/core/config.py`)
```python
# Enable/disable web search tools
ENABLE_WEB_SEARCH_TOOLS: bool = True

# Enable real-time exchange rates
ENABLE_REAL_TIME_EXCHANGE_RATES: bool = True

# Cache TTL for web search results
WEB_SEARCH_CACHE_TTL: int = 3600  # 1 hour
```

## Usage Examples

### Basic Currency Conversion
```python
from app.services.openai_search_tools import convert_currency_amount

result = await convert_currency_amount(1000000, "XPF", "GBP")
# Returns: {"success": true, "converted_amount": 7500.0, "calculation": "..."}
```

### Credit Classification
```python
from app.services.openai_search_tools import classify_credit_amount_with_conversion

result = await classify_credit_amount_with_conversion(1000000, "XPF", "GBP")
# Returns: {"classification": "Small Credit", "threshold_range": "< £50,000", ...}
```

### Integration with ValidationService
The ValidationService automatically detects currency-related questions and uses the appropriate LLM:

```python
# Currency-related question triggers search tools
question = "Check if there is a mismatch between company size and credit amount"

# ValidationService will:
# 1. Detect this is currency-related
# 2. Use LLM with search tools enabled
# 3. Allow LLM to call currency conversion functions
# 4. Provide comprehensive validation with real-time rates
```

## Error Handling

### API Failures
- Multiple API fallbacks ensure reliability
- Graceful degradation to cached or hardcoded rates
- Clear error messages for debugging

### Invalid Currencies
- Validation of currency codes
- Helpful error messages for unsupported currencies
- Fallback to common currency assumptions

### Network Issues
- Timeout handling for API calls
- Retry mechanisms with exponential backoff
- Offline mode with cached data

## Performance Considerations

### Caching Strategy
- Exchange rates cached for 1 hour
- LLM responses cached with search tool flag
- Efficient cache key generation

### API Rate Limiting
- Respects API rate limits
- Implements request throttling
- Uses multiple API providers for load distribution

### Optimization Flags
- `ENABLE_WEB_SEARCH_TOOLS`: Toggle function calling
- `ENABLE_REAL_TIME_EXCHANGE_RATES`: Use cached vs real-time rates
- `WEB_SEARCH_CACHE_TTL`: Adjust cache duration

## Testing

### Unit Tests
Run the test script to verify functionality:
```bash
cd ICP.AIAnalytics
python test_openai_search_tools.py
```

### Integration Tests
The test script includes:
- WebSearchService functionality
- OpenAI tool integration
- Currency conversion scenarios
- Complete validation workflow simulation

## Troubleshooting

### Common Issues

1. **No OpenAI API Key**
   - Ensure `OPENAI_API_KEY` is set in environment
   - Check API key validity and permissions

2. **Exchange Rate API Failures**
   - Verify internet connectivity
   - Check API key configuration
   - Review API rate limits

3. **Function Calling Not Working**
   - Ensure `ENABLE_WEB_SEARCH_TOOLS=True`
   - Verify LangChain version compatibility
   - Check OpenAI model supports function calling

4. **Currency Not Supported**
   - Add currency to supported list in WebSearchService
   - Implement custom exchange rate source
   - Use fallback conversion logic

### Debug Mode
Enable detailed logging by setting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

### Planned Features
- Support for more currency APIs
- Historical exchange rate analysis
- Currency trend detection
- Advanced financial calculations

### Extensibility
- Plugin architecture for new tools
- Custom validation rules
- Integration with external financial services
- Real-time market data integration

## Dependencies

### Required Packages
```
langchain==0.0.352
langchain-openai==0.0.2
langchain-community==0.0.13
httpx==0.25.2
requests==2.31.0
beautifulsoup4==4.12.2
```

### Optional Packages
```
# For enhanced caching
redis==4.5.1

# For advanced financial calculations
numpy==1.24.3
```

## Security Considerations

### API Key Management
- Store API keys securely in environment variables
- Use different keys for development/production
- Implement key rotation policies

### Data Privacy
- Exchange rate data is public information
- No sensitive financial data is transmitted
- Implement audit logging for compliance

### Rate Limiting
- Respect API provider terms of service
- Implement client-side rate limiting
- Monitor API usage and costs
