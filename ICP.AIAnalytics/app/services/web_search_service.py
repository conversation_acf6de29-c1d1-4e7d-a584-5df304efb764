"""
Web Search Service for OpenAI Function Calling Integration
Provides web search capabilities for exchange rates and currency conversion.
"""

import httpx
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
from app.core.config import settings


class WebSearchService:
    """Service for web search functionality with focus on currency and exchange rates."""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache for exchange rates
        
    async def search_exchange_rates(self, from_currency: str, to_currency: str = "GBP") -> Dict[str, Any]:
        """
        Search for current exchange rates between currencies.
        
        Args:
            from_currency: Source currency code (e.g., 'USD', 'EUR', 'XPF')
            to_currency: Target currency code (default: 'GBP')
            
        Returns:
            Dict containing exchange rate information
        """
        try:
            cache_key = f"{from_currency}_{to_currency}"
            
            # Check cache first
            if self._is_cache_valid(cache_key):
                print(f"Using cached exchange rate for {from_currency} to {to_currency}")
                return self.cache[cache_key]['data']
            
            # Try multiple exchange rate APIs
            rate_data = await self._fetch_exchange_rate_from_apis(from_currency, to_currency)
            
            if rate_data:
                # Cache the result
                self.cache[cache_key] = {
                    'data': rate_data,
                    'timestamp': datetime.now()
                }
                return rate_data
            else:
                return {
                    'success': False,
                    'error': f'Could not find exchange rate for {from_currency} to {to_currency}',
                    'rate': None
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Error fetching exchange rate: {str(e)}',
                'rate': None
            }
    
    async def search_currency_info(self, currency_code: str) -> Dict[str, Any]:
        """
        Search for information about a specific currency.
        
        Args:
            currency_code: Currency code to search for (e.g., 'XPF', 'USD')
            
        Returns:
            Dict containing currency information
        """
        try:
            cache_key = f"currency_info_{currency_code}"
            
            # Check cache first
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # Get currency information
            currency_info = await self._fetch_currency_info(currency_code)
            
            if currency_info:
                # Cache the result
                self.cache[cache_key] = {
                    'data': currency_info,
                    'timestamp': datetime.now()
                }
                return currency_info
            else:
                return {
                    'success': False,
                    'error': f'Could not find information for currency {currency_code}',
                    'info': None
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Error fetching currency info: {str(e)}',
                'info': None
            }
    
    async def convert_currency(self, amount: float, from_currency: str, to_currency: str = "GBP") -> Dict[str, Any]:
        """
        Convert an amount from one currency to another.
        
        Args:
            amount: Amount to convert
            from_currency: Source currency code
            to_currency: Target currency code (default: 'GBP')
            
        Returns:
            Dict containing conversion result
        """
        try:
            if from_currency.upper() == to_currency.upper():
                return {
                    'success': True,
                    'original_amount': amount,
                    'converted_amount': amount,
                    'from_currency': from_currency,
                    'to_currency': to_currency,
                    'exchange_rate': 1.0,
                    'calculation': f"{amount} {from_currency} = {amount} {to_currency} (same currency)"
                }
            
            # Get exchange rate
            rate_data = await self.search_exchange_rates(from_currency, to_currency)
            
            if rate_data.get('success') and rate_data.get('rate'):
                exchange_rate = rate_data['rate']
                converted_amount = amount * exchange_rate
                
                return {
                    'success': True,
                    'original_amount': amount,
                    'converted_amount': round(converted_amount, 2),
                    'from_currency': from_currency,
                    'to_currency': to_currency,
                    'exchange_rate': exchange_rate,
                    'calculation': f"{amount:,.2f} {from_currency} × {exchange_rate} = {converted_amount:,.2f} {to_currency}",
                    'source': rate_data.get('source', 'API')
                }
            else:
                return {
                    'success': False,
                    'error': f'Could not get exchange rate for {from_currency} to {to_currency}',
                    'original_amount': amount,
                    'from_currency': from_currency,
                    'to_currency': to_currency
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Error converting currency: {str(e)}',
                'original_amount': amount,
                'from_currency': from_currency,
                'to_currency': to_currency
            }
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_ttl
    
    async def _fetch_exchange_rate_from_apis(self, from_currency: str, to_currency: str) -> Optional[Dict[str, Any]]:
        """Fetch exchange rate from multiple API sources."""
        
        # Try exchangerate-api.com (free tier available)
        try:
            rate = await self._fetch_from_exchangerate_api(from_currency, to_currency)
            if rate:
                return rate
        except Exception as e:
            print(f"exchangerate-api.com failed: {e}")
        
        # Try fixer.io as fallback
        try:
            rate = await self._fetch_from_fixer_io(from_currency, to_currency)
            if rate:
                return rate
        except Exception as e:
            print(f"fixer.io failed: {e}")
        
        # Try a simple web scraping approach as last resort
        try:
            rate = await self._fetch_from_web_scraping(from_currency, to_currency)
            if rate:
                return rate
        except Exception as e:
            print(f"Web scraping failed: {e}")
        
        return None
    
    async def _fetch_from_exchangerate_api(self, from_currency: str, to_currency: str) -> Optional[Dict[str, Any]]:
        """Fetch from exchangerate-api.com (free tier)."""
        try:
            url = f"https://api.exchangerate-api.com/v4/latest/{from_currency.upper()}"
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url)
                
                if response.status_code == 200:
                    data = response.json()
                    rates = data.get('rates', {})
                    
                    if to_currency.upper() in rates:
                        return {
                            'success': True,
                            'rate': rates[to_currency.upper()],
                            'source': 'exchangerate-api.com',
                            'timestamp': datetime.now().isoformat()
                        }
        except Exception as e:
            print(f"Error fetching from exchangerate-api.com: {e}")
        
        return None
    
    async def _fetch_from_fixer_io(self, from_currency: str, to_currency: str) -> Optional[Dict[str, Any]]:
        """Fetch from fixer.io (requires API key)."""
        # This would require an API key from fixer.io
        # For now, return None to indicate this source is not available
        return None
    
    async def _fetch_from_web_scraping(self, from_currency: str, to_currency: str) -> Optional[Dict[str, Any]]:
        """Simple web scraping approach for exchange rates."""
        try:
            # This is a simplified example - in production, you'd want more robust scraping
            # For now, return a fallback rate for common currencies
            fallback_rates = {
                'USD_GBP': 0.79,
                'EUR_GBP': 0.86,
                'XPF_GBP': 0.0075,
                'JPY_GBP': 0.0067,
                'CAD_GBP': 0.58,
                'AUD_GBP': 0.52
            }
            
            rate_key = f"{from_currency.upper()}_{to_currency.upper()}"
            if rate_key in fallback_rates:
                return {
                    'success': True,
                    'rate': fallback_rates[rate_key],
                    'source': 'fallback_rates',
                    'timestamp': datetime.now().isoformat(),
                    'note': 'Using fallback rate - consider updating with real-time data'
                }
        except Exception as e:
            print(f"Error in web scraping fallback: {e}")
        
        return None
    
    async def _fetch_currency_info(self, currency_code: str) -> Optional[Dict[str, Any]]:
        """Fetch information about a currency."""
        # Currency information database
        currency_info = {
            'USD': {'name': 'US Dollar', 'country': 'United States', 'symbol': '$'},
            'EUR': {'name': 'Euro', 'country': 'European Union', 'symbol': '€'},
            'GBP': {'name': 'British Pound', 'country': 'United Kingdom', 'symbol': '£'},
            'JPY': {'name': 'Japanese Yen', 'country': 'Japan', 'symbol': '¥'},
            'XPF': {'name': 'CFP Franc', 'country': 'French Pacific Territories', 'symbol': 'F'},
            'CAD': {'name': 'Canadian Dollar', 'country': 'Canada', 'symbol': 'C$'},
            'AUD': {'name': 'Australian Dollar', 'country': 'Australia', 'symbol': 'A$'},
            'CHF': {'name': 'Swiss Franc', 'country': 'Switzerland', 'symbol': 'CHF'},
            'SEK': {'name': 'Swedish Krona', 'country': 'Sweden', 'symbol': 'kr'},
            'NOK': {'name': 'Norwegian Krone', 'country': 'Norway', 'symbol': 'kr'},
            'DKK': {'name': 'Danish Krone', 'country': 'Denmark', 'symbol': 'kr'},
        }
        
        code = currency_code.upper()
        if code in currency_info:
            return {
                'success': True,
                'currency_code': code,
                'info': currency_info[code]
            }
        else:
            return {
                'success': False,
                'currency_code': code,
                'error': f'Currency information not available for {code}'
            }
