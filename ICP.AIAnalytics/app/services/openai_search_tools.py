"""
OpenAI Function Calling Tools for Web Search and Currency Conversion
Integrates with LangChain to provide search capabilities for exchange rates.
"""

from langchain.tools import tool
from typing import Dict, Any, Optional, List
import json
from app.services.web_search_service import WebSearchService


# Initialize the web search service
web_search_service = WebSearchService()


@tool
async def search_exchange_rate(from_currency: str, to_currency: str = "GBP") -> str:
    """
    Search for current exchange rates between two currencies.
    
    Args:
        from_currency: Source currency code (e.g., 'USD', 'EUR', 'XPF')
        to_currency: Target currency code (default: 'GBP')
    
    Returns:
        JSON string containing exchange rate information
    """
    try:
        result = await web_search_service.search_exchange_rates(from_currency, to_currency)
        return json.dumps(result, indent=2)
    except Exception as e:
        return json.dumps({
            'success': False,
            'error': f'Error searching exchange rate: {str(e)}',
            'from_currency': from_currency,
            'to_currency': to_currency
        }, indent=2)


@tool
async def convert_currency_amount(amount: float, from_currency: str, to_currency: str = "GBP") -> str:
    """
    Convert a specific amount from one currency to another using current exchange rates.
    
    Args:
        amount: Amount to convert (e.g., 1000000)
        from_currency: Source currency code (e.g., 'XPF', 'USD', 'EUR')
        to_currency: Target currency code (default: 'GBP')
    
    Returns:
        JSON string containing conversion result with calculation details
    """
    try:
        result = await web_search_service.convert_currency(amount, from_currency, to_currency)
        return json.dumps(result, indent=2)
    except Exception as e:
        return json.dumps({
            'success': False,
            'error': f'Error converting currency: {str(e)}',
            'amount': amount,
            'from_currency': from_currency,
            'to_currency': to_currency
        }, indent=2)


@tool
async def get_currency_info(currency_code: str) -> str:
    """
    Get detailed information about a specific currency.
    
    Args:
        currency_code: Currency code to look up (e.g., 'XPF', 'USD', 'EUR')
    
    Returns:
        JSON string containing currency information
    """
    try:
        result = await web_search_service.search_currency_info(currency_code)
        return json.dumps(result, indent=2)
    except Exception as e:
        return json.dumps({
            'success': False,
            'error': f'Error getting currency info: {str(e)}',
            'currency_code': currency_code
        }, indent=2)


@tool
async def search_web_for_exchange_rates(query: str) -> str:
    """
    Perform a general web search for exchange rate information.
    
    Args:
        query: Search query related to exchange rates or currency conversion
    
    Returns:
        JSON string containing search results
    """
    try:
        # Extract currency codes from the query if possible
        import re
        currency_pattern = r'\b[A-Z]{3}\b'
        currencies = re.findall(currency_pattern, query.upper())
        
        if len(currencies) >= 2:
            # If we found currency codes, use our exchange rate search
            from_currency = currencies[0]
            to_currency = currencies[1] if len(currencies) > 1 else "GBP"
            
            result = await web_search_service.search_exchange_rates(from_currency, to_currency)
            result['search_query'] = query
            result['extracted_currencies'] = currencies
            
            return json.dumps(result, indent=2)
        else:
            # General search response
            return json.dumps({
                'success': True,
                'search_query': query,
                'message': 'For specific exchange rates, please provide currency codes (e.g., USD, EUR, XPF)',
                'suggestion': 'Use search_exchange_rate or convert_currency_amount tools for specific conversions'
            }, indent=2)
            
    except Exception as e:
        return json.dumps({
            'success': False,
            'error': f'Error performing web search: {str(e)}',
            'query': query
        }, indent=2)


@tool
async def classify_credit_amount_with_conversion(amount: float, currency: str, target_currency: str = "GBP") -> str:
    """
    Convert a credit amount to target currency and classify it as Small, Medium, or Large.
    
    Args:
        amount: Credit amount to classify
        currency: Currency of the amount
        target_currency: Target currency for classification (default: 'GBP')
    
    Returns:
        JSON string containing conversion and classification results
    """
    try:
        # Convert to target currency
        conversion_result = await web_search_service.convert_currency(amount, currency, target_currency)
        
        if conversion_result.get('success'):
            converted_amount = conversion_result['converted_amount']
            
            # Classify based on GBP thresholds
            if converted_amount < 50000:
                classification = "Small Credit"
                threshold = "< £50,000"
            elif converted_amount < 250000:
                classification = "Medium Credit"
                threshold = "£50,000 - £250,000"
            else:
                classification = "Large Credit"
                threshold = "> £250,000"
            
            result = {
                'success': True,
                'original_amount': amount,
                'original_currency': currency,
                'converted_amount': converted_amount,
                'target_currency': target_currency,
                'classification': classification,
                'threshold_range': threshold,
                'calculation': conversion_result.get('calculation', ''),
                'exchange_rate': conversion_result.get('exchange_rate'),
                'reasoning': f"{amount:,.2f} {currency} converts to {converted_amount:,.2f} {target_currency}, which is classified as {classification} ({threshold})"
            }
        else:
            result = {
                'success': False,
                'error': f'Could not convert {amount} {currency} to {target_currency}',
                'original_amount': amount,
                'original_currency': currency,
                'target_currency': target_currency
            }
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        return json.dumps({
            'success': False,
            'error': f'Error classifying credit amount: {str(e)}',
            'amount': amount,
            'currency': currency,
            'target_currency': target_currency
        }, indent=2)


# List of all available tools for easy import
SEARCH_TOOLS = [
    search_exchange_rate,
    convert_currency_amount,
    get_currency_info,
    search_web_for_exchange_rates,
    classify_credit_amount_with_conversion
]


def get_tool_descriptions() -> Dict[str, str]:
    """Get descriptions of all available search tools."""
    return {
        'search_exchange_rate': 'Search for current exchange rates between two currencies',
        'convert_currency_amount': 'Convert a specific amount from one currency to another',
        'get_currency_info': 'Get detailed information about a specific currency',
        'search_web_for_exchange_rates': 'Perform a general web search for exchange rate information',
        'classify_credit_amount_with_conversion': 'Convert and classify credit amounts using current exchange rates'
    }


def get_tool_schemas() -> List[Dict[str, Any]]:
    """Get OpenAI function schemas for all tools."""
    return [
        {
            "type": "function",
            "function": {
                "name": "search_exchange_rate",
                "description": "Search for current exchange rates between two currencies",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "from_currency": {
                            "type": "string",
                            "description": "Source currency code (e.g., 'USD', 'EUR', 'XPF')"
                        },
                        "to_currency": {
                            "type": "string",
                            "description": "Target currency code (default: 'GBP')",
                            "default": "GBP"
                        }
                    },
                    "required": ["from_currency"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "convert_currency_amount",
                "description": "Convert a specific amount from one currency to another using current exchange rates",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "amount": {
                            "type": "number",
                            "description": "Amount to convert (e.g., 1000000)"
                        },
                        "from_currency": {
                            "type": "string",
                            "description": "Source currency code (e.g., 'XPF', 'USD', 'EUR')"
                        },
                        "to_currency": {
                            "type": "string",
                            "description": "Target currency code (default: 'GBP')",
                            "default": "GBP"
                        }
                    },
                    "required": ["amount", "from_currency"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "classify_credit_amount_with_conversion",
                "description": "Convert a credit amount to target currency and classify it as Small, Medium, or Large",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "amount": {
                            "type": "number",
                            "description": "Credit amount to classify"
                        },
                        "currency": {
                            "type": "string",
                            "description": "Currency of the amount"
                        },
                        "target_currency": {
                            "type": "string",
                            "description": "Target currency for classification (default: 'GBP')",
                            "default": "GBP"
                        }
                    },
                    "required": ["amount", "currency"]
                }
            }
        }
    ]
