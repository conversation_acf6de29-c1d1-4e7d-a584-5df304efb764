#!/usr/bin/env python3
"""
Test script for OpenAI Search Tools integration with LangChain
Demonstrates currency conversion and exchange rate functionality.
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.web_search_service import WebSearchService
from app.services.openai_search_tools import (
    search_exchange_rate,
    convert_currency_amount,
    get_currency_info,
    classify_credit_amount_with_conversion
)


async def test_web_search_service():
    """Test the WebSearchService directly."""
    print("🔍 Testing WebSearchService...")
    print("=" * 50)
    
    service = WebSearchService()
    
    # Test 1: Search for exchange rates
    print("\n1. Testing exchange rate search (XPF to GBP):")
    result = await service.search_exchange_rates("XPF", "GBP")
    print(json.dumps(result, indent=2))
    
    # Test 2: Currency conversion
    print("\n2. Testing currency conversion (1,000,000 XPF to GBP):")
    result = await service.convert_currency(1000000, "XPF", "GBP")
    print(json.dumps(result, indent=2))
    
    # Test 3: Currency info
    print("\n3. Testing currency info (XPF):")
    result = await service.search_currency_info("XPF")
    print(json.dumps(result, indent=2))
    
    print("\n" + "=" * 50)


async def test_openai_tools():
    """Test the OpenAI function calling tools."""
    print("🛠️  Testing OpenAI Function Calling Tools...")
    print("=" * 50)
    
    # Test 1: Search exchange rate tool
    print("\n1. Testing search_exchange_rate tool:")
    result = await search_exchange_rate("USD", "GBP")
    print(result)
    
    # Test 2: Convert currency amount tool
    print("\n2. Testing convert_currency_amount tool:")
    result = await convert_currency_amount(1000000, "XPF", "GBP")
    print(result)
    
    # Test 3: Get currency info tool
    print("\n3. Testing get_currency_info tool:")
    result = await get_currency_info("XPF")
    print(result)
    
    # Test 4: Classify credit amount with conversion
    print("\n4. Testing classify_credit_amount_with_conversion tool:")
    result = await classify_credit_amount_with_conversion(1000000, "XPF", "GBP")
    print(result)
    
    print("\n" + "=" * 50)


async def test_currency_scenarios():
    """Test various currency conversion scenarios."""
    print("💱 Testing Currency Conversion Scenarios...")
    print("=" * 50)
    
    scenarios = [
        {"amount": 1000000, "currency": "XPF", "description": "Large XPF amount (should be Small in GBP)"},
        {"amount": 100000, "currency": "USD", "description": "Large USD amount (should be Large in GBP)"},
        {"amount": 50000, "currency": "EUR", "description": "Medium EUR amount (should be Medium in GBP)"},
        {"amount": 5000000, "currency": "JPY", "description": "Large JPY amount (should be Medium in GBP)"},
        {"amount": 30000, "currency": "GBP", "description": "Small GBP amount (should stay Small)"},
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['description']}:")
        result = await classify_credit_amount_with_conversion(
            scenario['amount'], 
            scenario['currency'], 
            "GBP"
        )
        
        # Parse and display key information
        try:
            data = json.loads(result)
            if data.get('success'):
                print(f"   Original: {data['original_amount']:,.2f} {data['original_currency']}")
                print(f"   Converted: {data['converted_amount']:,.2f} {data['target_currency']}")
                print(f"   Classification: {data['classification']}")
                print(f"   Calculation: {data['calculation']}")
            else:
                print(f"   Error: {data.get('error', 'Unknown error')}")
        except json.JSONDecodeError:
            print(f"   Raw result: {result}")
    
    print("\n" + "=" * 50)


async def test_integration_example():
    """Test a complete integration example simulating validation service usage."""
    print("🔗 Testing Integration Example...")
    print("=" * 50)
    
    # Simulate a validation question about company-credit mismatch
    question = "Check if there is a mismatch between company size and credit amount"
    
    # Simulate XML data with currency information
    xml_data = {
        "company": {
            "name": "Small Pacific Company",
            "employees": 25,
            "annual_turnover": {"amount": 500000, "currency": "EUR"}
        },
        "credit": {
            "max_amount": {"amount": 1000000, "currency": "XPF"},
            "limit": "1000000 XPF"
        },
        "exchange_rates": {
            "XPF_GBP": 0.0075,
            "EUR_GBP": 0.86
        }
    }
    
    print(f"Question: {question}")
    print(f"Company: {xml_data['company']['name']} ({xml_data['company']['employees']} employees)")
    print(f"Credit Amount: {xml_data['credit']['max_amount']['amount']:,} {xml_data['credit']['max_amount']['currency']}")
    
    # Test currency conversion for the credit amount
    print("\nConverting credit amount to GBP:")
    result = await classify_credit_amount_with_conversion(
        xml_data['credit']['max_amount']['amount'],
        xml_data['credit']['max_amount']['currency'],
        "GBP"
    )
    
    try:
        data = json.loads(result)
        if data.get('success'):
            print(f"✅ Conversion successful:")
            print(f"   {data['calculation']}")
            print(f"   Classification: {data['classification']}")
            print(f"   Reasoning: {data['reasoning']}")
            
            # Determine if there's a mismatch
            company_size = "Small" if xml_data['company']['employees'] < 50 else "Large"
            credit_classification = data['classification'].replace(" Credit", "")
            
            print(f"\n📊 Mismatch Analysis:")
            print(f"   Company Size: {company_size} (based on {xml_data['company']['employees']} employees)")
            print(f"   Credit Size: {credit_classification}")
            
            if company_size == credit_classification:
                print(f"   ✅ No mismatch detected - {company_size} company with {credit_classification} credit")
            else:
                print(f"   ⚠️  Mismatch detected - {company_size} company with {credit_classification} credit")
        else:
            print(f"❌ Conversion failed: {data.get('error')}")
    except json.JSONDecodeError:
        print(f"❌ Failed to parse result: {result}")
    
    print("\n" + "=" * 50)


async def main():
    """Run all tests."""
    print("🚀 OpenAI Search Tools Integration Test")
    print("Testing LangChain integration with web search for currency conversion")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    try:
        # Run all test suites
        await test_web_search_service()
        await test_openai_tools()
        await test_currency_scenarios()
        await test_integration_example()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
